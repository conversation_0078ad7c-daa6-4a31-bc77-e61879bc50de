#!/usr/bin/env python3
"""
抖音创作者平台上传工具 - 主程序入口
Douyin Creator Platform Upload Tool - Main Entry Point

使用方法:
    python main.py

Author: Claude 4.0 sonnet
Version: 1.0.0
"""

import sys
import os
import traceback

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主程序入口"""
    try:
        # 导入GUI主窗口
        from douyin_uploader.gui.main_window import MainWindow
        from douyin_uploader.config.settings import settings
        
        # 创建日志目录
        os.makedirs("logs", exist_ok=True)
        
        print("🚀 启动抖音创作者平台上传工具...")
        print(f"📁 工作目录: {os.getcwd()}")
        print(f"⚙️  配置文件: {settings.config_file}")
        
        # 启动GUI应用
        app = MainWindow()
        app.run()
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("💡 请确保已安装所有依赖包: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        print("📋 详细错误信息:")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
