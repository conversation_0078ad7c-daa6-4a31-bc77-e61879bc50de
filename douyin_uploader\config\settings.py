"""
配置管理模块
Configuration management module

管理应用程序的各种配置参数
Manages various configuration parameters for the application
"""

import os
import json
from typing import Dict, Any

class Settings:
    """应用程序配置管理类"""
    
    def __init__(self):
        self.config_file = "config.json"
        self.default_config = {
            # 抖音API配置
            "douyin": {
                "auth_url": "https://creator.douyin.com/web/api/media/upload/auth/v5/",
                "image_upload_url": "https://imagex.bytedanceapi.com/",
                "video_upload_url": "https://vod.bytedanceapi.com/",
                "service_id": "jm8ajry58r",
                "app_id": "2906"
            },
            
            # 上传配置
            "upload": {
                "chunk_size_mb": 3,  # 切片大小（MB）
                "max_retries": 3,    # 最大重试次数
                "timeout": 30,       # 请求超时时间（秒）
                "concurrent_chunks": 3  # 并发上传切片数
            },
            
            # GUI配置
            "gui": {
                "theme": "dark",
                "window_width": 800,
                "window_height": 600,
                "language": "zh_CN"
            },
            
            # 日志配置
            "logging": {
                "level": "INFO",
                "max_file_size_mb": 10,
                "backup_count": 5
            }
        }
        
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置和用户配置
                return self._merge_config(self.default_config, config)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                return self.default_config.copy()
        else:
            return self.default_config.copy()
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get(self, key: str, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self.config
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        config[keys[-1]] = value
        self.save_config()
    
    def _merge_config(self, default: Dict, user: Dict) -> Dict:
        """合并配置"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result

# 全局配置实例
settings = Settings()
