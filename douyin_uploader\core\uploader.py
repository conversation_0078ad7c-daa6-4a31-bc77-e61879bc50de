"""
抖音上传模块
Douyin Upload Module

实现文件上传的核心逻辑，包括切片上传和进度跟踪
Implements core file upload logic including chunked upload and progress tracking
"""

import os
import uuid
import time
import requests
from typing import Dict, List, Callable, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock

from .auth import DouyinAuth
from .utils import FileUtils, LogUtils
from ..config.settings import settings


class UploadProgress:
    """上传进度跟踪类"""
    
    def __init__(self, total_size: int):
        self.total_size = total_size
        self.uploaded_size = 0
        self.start_time = time.time()
        self.lock = Lock()
        self.callbacks = []
    
    def add_callback(self, callback: Callable[[int, int, float], None]):
        """添加进度回调函数"""
        self.callbacks.append(callback)
    
    def update(self, chunk_size: int):
        """更新上传进度"""
        with self.lock:
            self.uploaded_size += chunk_size
            elapsed_time = time.time() - self.start_time
            speed = self.uploaded_size / elapsed_time if elapsed_time > 0 else 0
            
            # 调用所有回调函数
            for callback in self.callbacks:
                try:
                    callback(self.uploaded_size, self.total_size, speed)
                except Exception as e:
                    LogUtils.error(f"进度回调函数执行失败: {e}")
    
    def get_progress(self) -> Tuple[int, int, float]:
        """获取当前进度"""
        with self.lock:
            elapsed_time = time.time() - self.start_time
            speed = self.uploaded_size / elapsed_time if elapsed_time > 0 else 0
            return self.uploaded_size, self.total_size, speed


class DouyinUploader:
    """抖音上传器"""
    
    def __init__(self, auth: DouyinAuth):
        """
        初始化上传器
        
        Args:
            auth: 认证对象
        """
        self.auth = auth
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 Edg/122.0.0.0'
        })
    
    def upload_file(self, file_path: str, 
                   progress_callback: Optional[Callable[[int, int, float], None]] = None) -> Dict:
        """
        上传文件
        
        Args:
            file_path: 文件路径
            progress_callback: 进度回调函数 (uploaded, total, speed)
            
        Returns:
            上传结果字典
        """
        # 验证文件
        is_valid, error_msg = FileUtils.validate_file(file_path)
        if not is_valid:
            raise Exception(f"文件验证失败: {error_msg}")
        
        file_type = FileUtils.get_file_type(file_path)
        file_size = FileUtils.get_file_size(file_path)
        
        LogUtils.info(f"开始上传文件: {file_path}")
        LogUtils.info(f"文件类型: {file_type}, 大小: {FileUtils.format_file_size(file_size)}")
        
        # 创建进度跟踪器
        progress = UploadProgress(file_size)
        if progress_callback:
            progress.add_callback(progress_callback)
        
        try:
            if file_type == 'image':
                return self._upload_image(file_path, progress)
            elif file_type == 'video':
                return self._upload_video(file_path, progress)
            else:
                raise Exception(f"不支持的文件类型: {file_type}")
                
        except Exception as e:
            LogUtils.error(f"文件上传失败: {e}")
            raise
    
    def _upload_image(self, file_path: str, progress: UploadProgress) -> Dict:
        """
        上传图片文件
        
        Args:
            file_path: 图片文件路径
            progress: 进度跟踪器
            
        Returns:
            上传结果
        """
        # 获取图片上传分配
        allocation = self.auth.get_image_upload_allocation()
        
        # 构建上传URL
        upload_host = allocation['UploadHosts'][0]
        store_uri = allocation['StoreUri']
        upload_url = f"https://{upload_host}/upload/v1/{store_uri}"
        
        # 读取文件数据
        with open(file_path, 'rb') as f:
            file_data = f.read()
        
        # 计算CRC32
        crc32 = FileUtils.calculate_crc32(file_data)
        
        # 构建请求头
        headers = {
            'Authorization': allocation['Auth'],
            'Content-CRC32': crc32,
            'User-Agent': self.session.headers['User-Agent']
        }
        
        # 上传文件
        response = self.session.post(upload_url, data=file_data, headers=headers)
        response.raise_for_status()
        
        result = response.json()
        if result.get('code') == 2000:
            progress.update(len(file_data))
            LogUtils.info(f"图片上传成功: {store_uri}")
            return {
                'success': True,
                'store_uri': store_uri,
                'upload_url': upload_url,
                'file_type': 'image'
            }
        else:
            raise Exception(f"图片上传失败: {result}")
    
    def _upload_video(self, file_path: str, progress: UploadProgress) -> Dict:
        """
        上传视频文件
        
        Args:
            file_path: 视频文件路径
            progress: 进度跟踪器
            
        Returns:
            上传结果
        """
        file_size = FileUtils.get_file_size(file_path)
        
        # 获取视频上传分配
        allocation = self.auth.get_video_upload_allocation(file_size)
        
        # 检查是否需要切片上传
        chunk_size_mb = settings.get('upload.chunk_size_mb', 3)
        chunk_size = chunk_size_mb * 1024 * 1024
        
        if file_size <= chunk_size:
            # 小文件直接上传
            return self._upload_video_direct(file_path, allocation, progress)
        else:
            # 大文件切片上传
            return self._upload_video_chunked(file_path, allocation, progress, chunk_size)
    
    def _upload_video_direct(self, file_path: str, allocation: Dict, 
                           progress: UploadProgress) -> Dict:
        """
        直接上传视频文件
        
        Args:
            file_path: 视频文件路径
            allocation: 上传分配信息
            progress: 进度跟踪器
            
        Returns:
            上传结果
        """
        # 构建上传URL
        upload_host = allocation['UploadHosts'][0]
        store_uri = allocation['StoreUri']
        upload_url = f"https://{upload_host}/upload/v1/{store_uri}"
        
        # 读取文件数据
        with open(file_path, 'rb') as f:
            file_data = f.read()
        
        # 计算CRC32
        crc32 = FileUtils.calculate_crc32(file_data)
        
        # 构建请求头
        headers = {
            'Authorization': allocation['Auth'],
            'Content-CRC32': crc32,
            'User-Agent': self.session.headers['User-Agent']
        }
        
        # 上传文件
        response = self.session.post(upload_url, data=file_data, headers=headers)
        response.raise_for_status()
        
        result = response.json()
        if result.get('code') == 2000:
            progress.update(len(file_data))
            LogUtils.info(f"视频上传成功: {store_uri}")
            return {
                'success': True,
                'store_uri': store_uri,
                'upload_url': upload_url,
                'file_type': 'video'
            }
        else:
            raise Exception(f"视频上传失败: {result}")
    
    def _upload_video_chunked(self, file_path: str, allocation: Dict, 
                            progress: UploadProgress, chunk_size: int) -> Dict:
        """
        切片上传视频文件
        
        Args:
            file_path: 视频文件路径
            allocation: 上传分配信息
            progress: 进度跟踪器
            chunk_size: 切片大小
            
        Returns:
            上传结果
        """
        # 读取文件并分块
        chunks = FileUtils.read_file_chunks(file_path, chunk_size)
        total_chunks = len(chunks)
        
        LogUtils.info(f"开始切片上传，共{total_chunks}个切片")
        
        # 生成上传ID
        upload_id = str(uuid.uuid4())
        
        # 构建上传URL
        upload_host = allocation['UploadHosts'][0]
        store_uri = allocation['StoreUri']
        
        # 并发上传切片
        max_workers = settings.get('upload.concurrent_chunks', 3)
        max_retries = settings.get('upload.max_retries', 3)
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有切片上传任务
            future_to_chunk = {}
            for i, (offset, data) in enumerate(chunks):
                future = executor.submit(
                    self._upload_chunk_with_retry,
                    upload_host, store_uri, allocation['Auth'],
                    upload_id, i + 1, offset, data, max_retries
                )
                future_to_chunk[future] = (i + 1, len(data))
            
            # 等待所有切片上传完成
            completed_chunks = 0
            for future in as_completed(future_to_chunk):
                chunk_num, chunk_size = future_to_chunk[future]
                try:
                    result = future.result()
                    if result:
                        completed_chunks += 1
                        progress.update(chunk_size)
                        LogUtils.debug(f"切片 {chunk_num}/{total_chunks} 上传成功")
                    else:
                        raise Exception(f"切片 {chunk_num} 上传失败")
                except Exception as e:
                    LogUtils.error(f"切片 {chunk_num} 上传异常: {e}")
                    raise
        
        if completed_chunks == total_chunks:
            LogUtils.info(f"视频切片上传成功: {store_uri}")
            return {
                'success': True,
                'store_uri': store_uri,
                'upload_url': f"https://{upload_host}/upload/v1/{store_uri}",
                'file_type': 'video',
                'chunks': total_chunks
            }
        else:
            raise Exception(f"切片上传不完整: {completed_chunks}/{total_chunks}")
    
    def _upload_chunk_with_retry(self, upload_host: str, store_uri: str, auth: str,
                               upload_id: str, part_number: int, offset: int, 
                               data: bytes, max_retries: int) -> bool:
        """
        带重试的切片上传
        
        Args:
            upload_host: 上传主机
            store_uri: 存储URI
            auth: 认证信息
            upload_id: 上传ID
            part_number: 切片编号
            offset: 偏移量
            data: 切片数据
            max_retries: 最大重试次数
            
        Returns:
            是否上传成功
        """
        upload_url = f"https://{upload_host}/upload/v1/{store_uri}"
        
        for attempt in range(max_retries + 1):
            try:
                # 构建请求参数
                params = {
                    'uploadid': upload_id,
                    'part_number': part_number,
                    'part_offset': offset,
                    'phase': 'transfer'
                }
                
                # 计算CRC32
                crc32 = FileUtils.calculate_crc32(data)
                
                # 构建请求头
                headers = {
                    'Authorization': auth,
                    'Content-CRC32': crc32,
                    'User-Agent': self.session.headers['User-Agent']
                }
                
                # 上传切片
                response = self.session.post(
                    upload_url, 
                    params=params,
                    data=data, 
                    headers=headers,
                    timeout=settings.get('upload.timeout', 30)
                )
                response.raise_for_status()
                
                result = response.json()
                if result.get('code') == 2000:
                    return True
                else:
                    raise Exception(f"切片上传失败: {result}")
                    
            except Exception as e:
                if attempt < max_retries:
                    LogUtils.warning(f"切片 {part_number} 上传失败，重试 {attempt + 1}/{max_retries}: {e}")
                    time.sleep(1)  # 等待1秒后重试
                else:
                    LogUtils.error(f"切片 {part_number} 上传最终失败: {e}")
                    return False
        
        return False
