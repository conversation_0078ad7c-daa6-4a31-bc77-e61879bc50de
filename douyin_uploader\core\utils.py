"""
工具函数模块
Utility Functions Module

提供文件处理、日志记录等通用功能
Provides common functionality for file processing, logging, etc.
"""

import os
import zlib
import logging
import mimetypes
from typing import List, Tuple, Optional
from pathlib import Path

from ..config.settings import settings


class FileUtils:
    """文件处理工具类"""
    
    # 支持的图片格式
    IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
    
    # 支持的视频格式
    VIDEO_EXTENSIONS = {'.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm'}
    
    @staticmethod
    def get_file_type(file_path: str) -> str:
        """
        获取文件类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            'image' 或 'video' 或 'unknown'
        """
        ext = Path(file_path).suffix.lower()
        
        if ext in FileUtils.IMAGE_EXTENSIONS:
            return 'image'
        elif ext in FileUtils.VIDEO_EXTENSIONS:
            return 'video'
        else:
            return 'unknown'
    
    @staticmethod
    def get_file_size(file_path: str) -> int:
        """
        获取文件大小
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件大小（字节）
        """
        return os.path.getsize(file_path)
    
    @staticmethod
    def calculate_crc32(data: bytes) -> str:
        """
        计算数据的CRC32值
        
        Args:
            data: 二进制数据
            
        Returns:
            CRC32十六进制字符串
        """
        return hex(zlib.crc32(data) & 0xFFFFFFFF)[2:]
    
    @staticmethod
    def read_file_chunks(file_path: str, chunk_size: int) -> List[Tuple[int, bytes]]:
        """
        读取文件并分块
        
        Args:
            file_path: 文件路径
            chunk_size: 块大小（字节）
            
        Returns:
            [(偏移量, 数据块), ...]
        """
        chunks = []
        
        with open(file_path, 'rb') as f:
            offset = 0
            while True:
                data = f.read(chunk_size)
                if not data:
                    break
                chunks.append((offset, data))
                offset += len(data)
        
        return chunks
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        格式化文件大小显示
        
        Args:
            size_bytes: 文件大小（字节）
            
        Returns:
            格式化的大小字符串
        """
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f}{size_names[i]}"
    
    @staticmethod
    def validate_file(file_path: str) -> Tuple[bool, str]:
        """
        验证文件是否有效
        
        Args:
            file_path: 文件路径
            
        Returns:
            (是否有效, 错误信息)
        """
        if not os.path.exists(file_path):
            return False, "文件不存在"
        
        if not os.path.isfile(file_path):
            return False, "不是有效的文件"
        
        file_type = FileUtils.get_file_type(file_path)
        if file_type == 'unknown':
            return False, "不支持的文件格式"
        
        file_size = FileUtils.get_file_size(file_path)
        if file_size == 0:
            return False, "文件为空"
        
        # 检查文件大小限制（例如：最大2GB）
        max_size = 2 * 1024 * 1024 * 1024  # 2GB
        if file_size > max_size:
            return False, f"文件过大，最大支持{FileUtils.format_file_size(max_size)}"
        
        return True, ""


class LogUtils:
    """日志工具类"""
    
    _logger = None
    
    @classmethod
    def get_logger(cls) -> logging.Logger:
        """获取日志记录器"""
        if cls._logger is None:
            cls._setup_logger()
        return cls._logger
    
    @classmethod
    def _setup_logger(cls):
        """设置日志记录器"""
        # 创建日志记录器
        cls._logger = logging.getLogger('douyin_uploader')
        cls._logger.setLevel(getattr(logging, settings.get('logging.level', 'INFO')))
        
        # 避免重复添加处理器
        if cls._logger.handlers:
            return
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        cls._logger.addHandler(console_handler)
        
        # 文件处理器
        try:
            from logging.handlers import RotatingFileHandler
            
            log_file = os.path.join('logs', 'douyin_uploader.log')
            max_bytes = settings.get('logging.max_file_size_mb', 10) * 1024 * 1024
            backup_count = settings.get('logging.backup_count', 5)
            
            file_handler = RotatingFileHandler(
                log_file, 
                maxBytes=max_bytes,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            cls._logger.addHandler(file_handler)
            
        except Exception as e:
            print(f"设置文件日志失败: {e}")
    
    @classmethod
    def info(cls, message: str):
        """记录信息日志"""
        cls.get_logger().info(message)
    
    @classmethod
    def error(cls, message: str):
        """记录错误日志"""
        cls.get_logger().error(message)
    
    @classmethod
    def warning(cls, message: str):
        """记录警告日志"""
        cls.get_logger().warning(message)
    
    @classmethod
    def debug(cls, message: str):
        """记录调试日志"""
        cls.get_logger().debug(message)
