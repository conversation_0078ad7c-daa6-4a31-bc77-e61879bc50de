# 使用说明

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行程序
```bash
python main.py
```

### 3. 配置Cookie
1. 打开抖音创作者平台 (https://creator.douyin.com/)
2. 登录你的账号
3. 按F12打开开发者工具
4. 切换到Network标签
5. 刷新页面，找到任意请求
6. 复制Cookie值到程序中

### 4. 上传文件
1. 点击"选择文件"按钮选择要上传的图片或视频
2. 确认Cookie已设置
3. 点击"开始上传"按钮
4. 等待上传完成，获取上传地址

## 📋 支持的文件格式

### 图片格式
- JPG/JPEG
- PNG
- GIF
- BMP
- WebP

### 视频格式
- MP4
- AVI
- MOV
- WMV
- FLV
- MKV
- WebM

## ⚙️ 配置说明

程序会自动创建 `config.json` 配置文件，可以修改以下参数：

```json
{
  "upload": {
    "chunk_size_mb": 3,        // 切片大小（MB）
    "max_retries": 3,          // 最大重试次数
    "timeout": 30,             // 请求超时时间（秒）
    "concurrent_chunks": 3     // 并发上传切片数
  },
  "gui": {
    "theme": "dark",           // 界面主题
    "window_width": 800,       // 窗口宽度
    "window_height": 600       // 窗口高度
  }
}
```

## 🔧 高级功能

### 大文件切片上传
- 视频文件超过3MB会自动切片上传
- 支持断点续传
- 并发上传提高速度

### 进度显示
- 实时显示上传进度
- 显示上传速度
- 显示剩余时间

### 错误处理
- 自动重试失败的切片
- 详细的错误信息显示
- 日志记录所有操作

## 📝 注意事项

1. **Cookie有效性**
   - Cookie有时效性，过期后需要重新获取
   - 建议定期更新Cookie

2. **网络连接**
   - 确保网络连接稳定
   - 大文件上传可能需要较长时间

3. **文件大小限制**
   - 单个文件最大支持2GB
   - 建议视频文件不超过1GB

4. **上传速度**
   - 上传速度取决于网络带宽
   - 可以调整并发切片数来优化速度

## 🐛 常见问题

### Q: Cookie如何获取？
A: 
1. 登录抖音创作者平台
2. F12打开开发者工具
3. Network标签下找到请求
4. 复制Cookie头部的值

### Q: 上传失败怎么办？
A: 
1. 检查Cookie是否有效
2. 检查网络连接
3. 查看错误日志
4. 尝试重新上传

### Q: 支持批量上传吗？
A: 当前版本不支持批量上传，需要逐个上传文件

### Q: 上传的文件在哪里？
A: 文件上传到抖音的服务器，程序会返回存储URI和上传地址

## 📞 技术支持

如果遇到问题，请：
1. 查看日志文件 `logs/douyin_uploader.log`
2. 运行测试脚本 `python test_basic.py`
3. 检查依赖包是否正确安装

---

**开发者**: Claude 4.0 sonnet  
**版本**: 1.0.0  
**更新时间**: 2025-07-18
