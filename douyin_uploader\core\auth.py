"""
抖音认证模块
Douyin Authentication Module

实现AWS4-HMAC-SHA256签名算法和上传授权
Implements AWS4-HMAC-SHA256 signature algorithm and upload authorization
"""

import datetime
import hashlib
import hmac
import json
import random
import string
import requests
from typing import Dict, Optional, Tuple
from urllib.parse import urlencode

from ..config.settings import settings


class DouyinAuth:
    """抖音认证类"""
    
    def __init__(self, cookie: str):
        """
        初始化认证类
        
        Args:
            cookie: 抖音登录Cookie
        """
        self.cookie = cookie
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 Edg/122.0.0.0',
            'Cookie': cookie
        })
        
        # 缓存授权信息
        self._auth_cache = None
        self._auth_expire_time = None
    
    def get_upload_auth(self) -> Dict:
        """
        获取上传授权信息
        
        Returns:
            包含ak和auth字段的字典
        """
        # 检查缓存是否有效
        if self._auth_cache and self._auth_expire_time:
            if datetime.datetime.now() < self._auth_expire_time:
                return self._auth_cache
        
        auth_url = settings.get('douyin.auth_url')
        
        try:
            response = self.session.get(auth_url)
            response.raise_for_status()
            
            data = response.json()
            if data.get('status_code') == 0:
                # 解析auth字段中的JSON
                auth_info = json.loads(data['auth'])
                
                # 计算过期时间（提前5分钟刷新）
                expire_time_str = auth_info.get('ExpiredTime', '')
                if expire_time_str:
                    expire_time = datetime.datetime.fromisoformat(expire_time_str.replace('+08:00', ''))
                    self._auth_expire_time = expire_time - datetime.timedelta(minutes=5)
                
                self._auth_cache = {
                    'ak': data['ak'],
                    'auth': data['auth'],
                    'auth_info': auth_info
                }
                
                return self._auth_cache
            else:
                raise Exception(f"获取授权失败: {data}")
                
        except Exception as e:
            raise Exception(f"获取上传授权失败: {e}")
    
    def get_image_upload_allocation(self) -> Dict:
        """
        获取图片上传资源分配
        
        Returns:
            包含上传地址和认证信息的字典
        """
        auth_data = self.get_upload_auth()
        auth_info = auth_data['auth_info']
        
        # 生成请求参数
        params = {
            "Action": "ApplyImageUpload",
            "ServiceId": settings.get('douyin.service_id'),
            "Version": "2018-08-01",
            "app_id": settings.get('douyin.app_id'),
            "s": self._generate_random_string(),
            "user_id": ""
        }
        
        # 生成AWS签名
        authorization = self._generate_aws_signature(
            auth_info, params, 'imagex', 'cn-north-1'
        )
        
        # 构建请求
        base_url = settings.get('douyin.image_upload_url')
        url = f"{base_url}?{urlencode(params)}"
        
        headers = {
            'accept': '*/*',
            'authorization': authorization,
            'user-agent': self.session.headers['User-Agent'],
            'x-amz-date': self._get_amz_date(),
            'x-amz-security-token': auth_info['SessionToken']
        }
        
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            data = response.json()
            if 'Result' in data:
                return data['Result']
            else:
                raise Exception(f"获取图片上传分配失败: {data}")
                
        except Exception as e:
            raise Exception(f"获取图片上传分配失败: {e}")
    
    def get_video_upload_allocation(self, file_size: int) -> Dict:
        """
        获取视频上传资源分配
        
        Args:
            file_size: 文件大小（字节）
            
        Returns:
            包含上传地址和认证信息的字典
        """
        auth_data = self.get_upload_auth()
        auth_info = auth_data['auth_info']
        
        # 生成请求参数
        params = {
            'Action': 'ApplyUploadInner',
            'FileSize': str(file_size),
            'FileType': 'video',
            'IsInner': '1',
            'SpaceName': 'aweme',
            'Version': '2020-11-19',
            'app_id': settings.get('douyin.app_id'),
            's': self._generate_random_string(),
            'user_id': ''
        }
        
        # 生成AWS签名
        authorization = self._generate_aws_signature(
            auth_info, params, 'vod', 'cn-north-1'
        )
        
        # 构建请求
        base_url = settings.get('douyin.video_upload_url')
        url = f"{base_url}?{urlencode(params)}"
        
        headers = {
            'accept': '*/*',
            'authorization': authorization,
            'user-agent': self.session.headers['User-Agent'],
            'x-amz-date': self._get_amz_date(),
            'x-amz-security-token': auth_info['SessionToken']
        }
        
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            data = response.json()
            if 'Result' in data:
                return data['Result']
            else:
                raise Exception(f"获取视频上传分配失败: {data}")
                
        except Exception as e:
            raise Exception(f"获取视频上传分配失败: {e}")
    
    def _generate_aws_signature(self, auth_info: Dict, params: Dict, 
                               service: str, region: str) -> str:
        """
        生成AWS4-HMAC-SHA256签名
        
        Args:
            auth_info: 认证信息
            params: 请求参数
            service: 服务名称
            region: 区域名称
            
        Returns:
            Authorization头部值
        """
        # 获取认证信息
        access_key_id = auth_info['AccessKeyID']
        secret_access_key = auth_info['SecretAccessKey']
        session_token = auth_info['SessionToken']
        
        # 时间信息
        amz_date = self._get_amz_date()
        date_stamp = amz_date[:8]
        
        # 构建规范查询字符串
        canonical_querystring = urlencode(sorted(params.items()))
        
        # 构建规范头部
        canonical_headers = (
            f'x-amz-date:{amz_date}\n'
            f'x-amz-security-token:{session_token}\n'
        )
        
        # 构建规范请求
        canonical_request = (
            'GET\n'
            '/\n'
            f'{canonical_querystring}\n'
            f'{canonical_headers}\n'
            'x-amz-date;x-amz-security-token\n'
            f'{hashlib.sha256("".encode("utf-8")).hexdigest()}'
        )
        
        # 构建签名字符串
        algorithm = 'AWS4-HMAC-SHA256'
        credential_scope = f'{date_stamp}/{region}/{service}/aws4_request'
        string_to_sign = (
            f'{algorithm}\n'
            f'{amz_date}\n'
            f'{credential_scope}\n'
            f'{hashlib.sha256(canonical_request.encode("utf-8")).hexdigest()}'
        )
        
        # 生成签名密钥
        signing_key = self._get_signature_key(
            secret_access_key, date_stamp, region, service
        )
        
        # 计算签名
        signature = hmac.new(
            signing_key, 
            string_to_sign.encode("utf-8"), 
            hashlib.sha256
        ).hexdigest()
        
        # 构建Authorization头部
        authorization = (
            f'{algorithm} '
            f'Credential={access_key_id}/{credential_scope}, '
            f'SignedHeaders=x-amz-date;x-amz-security-token, '
            f'Signature={signature}'
        )
        
        return authorization
    
    def _get_signature_key(self, key: str, date_stamp: str, 
                          region_name: str, service_name: str) -> bytes:
        """生成签名密钥"""
        k_date = hmac.new(
            f'AWS4{key}'.encode('utf-8'), 
            date_stamp.encode("utf-8"), 
            hashlib.sha256
        ).digest()
        
        k_region = hmac.new(
            k_date, 
            region_name.encode("utf-8"), 
            hashlib.sha256
        ).digest()
        
        k_service = hmac.new(
            k_region, 
            service_name.encode("utf-8"), 
            hashlib.sha256
        ).digest()
        
        k_signing = hmac.new(
            k_service, 
            'aws4_request'.encode("utf-8"), 
            hashlib.sha256
        ).digest()
        
        return k_signing
    
    def _get_amz_date(self) -> str:
        """获取AMZ日期格式"""
        return datetime.datetime.utcnow().strftime('%Y%m%dT%H%M%SZ')
    
    def _generate_random_string(self, length: int = 11) -> str:
        """生成随机字符串"""
        chars = string.digits + string.ascii_lowercase
        return ''.join(random.choice(chars) for _ in range(length))
