# 抖音创作者平台上传工具

一个现代化的抖音视频和图文上传工具，支持大文件切片上传。

## ✨ 特性

- 🚀 **现代化界面**: 基于CustomTkinter的深色主题GUI
- 📤 **智能上传**: 自动检测文件类型，支持图片和视频上传
- ✂️ **切片上传**: 大视频文件自动切片上传，支持断点续传
- 🔐 **安全认证**: AWS4-HMAC-SHA256签名算法，确保上传安全
- 📊 **实时进度**: 实时显示上传进度和速度
- 🔄 **错误重试**: 智能重试机制，提高上传成功率
- 📋 **批量处理**: 支持多文件批量上传

## 🛠️ 安装

1. 克隆项目
```bash
git clone <repository-url>
cd douyin-uploader
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

## 🚀 使用方法

1. 运行程序
```bash
python main.py
```

2. 在界面中设置抖音Cookie
3. 选择要上传的文件
4. 点击上传按钮

## 📁 项目结构

```
douyin_uploader/
├── core/                 # 核心功能模块
│   ├── auth.py          # 认证和签名
│   ├── uploader.py      # 上传逻辑
│   └── utils.py         # 工具函数
├── gui/                 # 图形界面模块
│   ├── main_window.py   # 主窗口
│   └── components.py    # UI组件
├── config/              # 配置管理
│   └── settings.py      # 配置文件
├── logs/               # 日志目录
├── requirements.txt    # 依赖包
└── main.py            # 程序入口
```

## ⚙️ 配置

程序会自动创建 `config.json` 配置文件，可以修改以下参数：

- `upload.chunk_size_mb`: 切片大小（默认3MB）
- `upload.max_retries`: 最大重试次数（默认3次）
- `upload.concurrent_chunks`: 并发上传切片数（默认3个）

## 📝 注意事项

1. 需要有效的抖音创作者平台Cookie
2. 确保网络连接稳定
3. 大文件上传可能需要较长时间

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

---

**Author**: Claude 4.0 sonnet  
**Version**: 1.0.0
