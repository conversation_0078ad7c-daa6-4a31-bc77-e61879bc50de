"""
GUI组件模块
GUI Components Module

提供自定义的UI组件
Provides custom UI components
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import customtkinter as ctk
from typing import Callable, Optional, List
import os

from ..core.utils import FileUtils


class FileSelector(ctk.CTkFrame):
    """文件选择器组件"""
    
    def __init__(self, parent, on_file_selected: Optional[Callable[[str], None]] = None):
        super().__init__(parent)
        
        self.on_file_selected = on_file_selected
        self.selected_file = None
        
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        # 标题
        title_label = ctk.CTkLabel(
            self, 
            text="📁 文件选择", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=(10, 5))
        
        # 文件路径显示
        self.file_path_var = tk.StringVar(value="请选择要上传的文件...")
        self.file_path_label = ctk.CTkLabel(
            self,
            textvariable=self.file_path_var,
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        self.file_path_label.pack(pady=5, padx=20, fill="x")
        
        # 按钮框架
        button_frame = ctk.CTkFrame(self, fg_color="transparent")
        button_frame.pack(pady=10, fill="x")
        
        # 选择文件按钮
        self.select_button = ctk.CTkButton(
            button_frame,
            text="选择文件",
            command=self._select_file,
            width=120,
            height=35
        )
        self.select_button.pack(side="left", padx=(20, 10))
        
        # 清除按钮
        self.clear_button = ctk.CTkButton(
            button_frame,
            text="清除",
            command=self._clear_file,
            width=80,
            height=35,
            fg_color="gray",
            hover_color="darkgray"
        )
        self.clear_button.pack(side="left", padx=10)
        
        # 文件信息显示
        self.info_label = ctk.CTkLabel(
            self,
            text="",
            font=ctk.CTkFont(size=11),
            text_color="lightblue"
        )
        self.info_label.pack(pady=(5, 10))
    
    def _select_file(self):
        """选择文件"""
        file_types = [
            ("所有支持的文件", "*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.webp;*.mp4;*.avi;*.mov;*.wmv;*.flv;*.mkv;*.webm"),
            ("图片文件", "*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.webp"),
            ("视频文件", "*.mp4;*.avi;*.mov;*.wmv;*.flv;*.mkv;*.webm"),
            ("所有文件", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="选择要上传的文件",
            filetypes=file_types
        )
        
        if file_path:
            self._set_selected_file(file_path)
    
    def _set_selected_file(self, file_path: str):
        """设置选中的文件"""
        # 验证文件
        is_valid, error_msg = FileUtils.validate_file(file_path)
        if not is_valid:
            messagebox.showerror("文件错误", error_msg)
            return
        
        self.selected_file = file_path
        
        # 更新显示
        filename = os.path.basename(file_path)
        self.file_path_var.set(filename)
        self.file_path_label.configure(text_color="white")
        
        # 显示文件信息
        file_type = FileUtils.get_file_type(file_path)
        file_size = FileUtils.get_file_size(file_path)
        size_str = FileUtils.format_file_size(file_size)
        
        type_emoji = "🖼️" if file_type == "image" else "🎬"
        info_text = f"{type_emoji} {file_type.upper()} | {size_str}"
        self.info_label.configure(text=info_text)
        
        # 调用回调函数
        if self.on_file_selected:
            self.on_file_selected(file_path)
    
    def _clear_file(self):
        """清除选中的文件"""
        self.selected_file = None
        self.file_path_var.set("请选择要上传的文件...")
        self.file_path_label.configure(text_color="gray")
        self.info_label.configure(text="")
        
        if self.on_file_selected:
            self.on_file_selected(None)
    
    def get_selected_file(self) -> Optional[str]:
        """获取选中的文件路径"""
        return self.selected_file


class ProgressBar(ctk.CTkFrame):
    """进度条组件"""
    
    def __init__(self, parent):
        super().__init__(parent)
        
        self._setup_ui()
        self.reset()
    
    def _setup_ui(self):
        """设置UI"""
        # 标题
        title_label = ctk.CTkLabel(
            self, 
            text="📊 上传进度", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=(10, 5))
        
        # 进度条
        self.progress_bar = ctk.CTkProgressBar(self, width=400, height=20)
        self.progress_bar.pack(pady=10, padx=20, fill="x")
        
        # 进度信息
        self.progress_label = ctk.CTkLabel(
            self,
            text="等待上传...",
            font=ctk.CTkFont(size=12)
        )
        self.progress_label.pack(pady=5)
        
        # 速度信息
        self.speed_label = ctk.CTkLabel(
            self,
            text="",
            font=ctk.CTkFont(size=11),
            text_color="lightblue"
        )
        self.speed_label.pack(pady=(0, 10))
    
    def update_progress(self, uploaded: int, total: int, speed: float):
        """
        更新进度
        
        Args:
            uploaded: 已上传字节数
            total: 总字节数
            speed: 上传速度（字节/秒）
        """
        if total > 0:
            progress = uploaded / total
            self.progress_bar.set(progress)
            
            # 更新进度文本
            uploaded_str = FileUtils.format_file_size(uploaded)
            total_str = FileUtils.format_file_size(total)
            percentage = progress * 100
            
            progress_text = f"{uploaded_str} / {total_str} ({percentage:.1f}%)"
            self.progress_label.configure(text=progress_text)
            
            # 更新速度文本
            if speed > 0:
                speed_str = FileUtils.format_file_size(int(speed))
                remaining_bytes = total - uploaded
                eta_seconds = remaining_bytes / speed if speed > 0 else 0
                eta_str = self._format_time(eta_seconds)
                
                speed_text = f"速度: {speed_str}/s | 剩余时间: {eta_str}"
                self.speed_label.configure(text=speed_text)
    
    def set_status(self, status: str, color: str = "white"):
        """设置状态文本"""
        self.progress_label.configure(text=status, text_color=color)
        self.speed_label.configure(text="")
    
    def reset(self):
        """重置进度条"""
        self.progress_bar.set(0)
        self.progress_label.configure(text="等待上传...", text_color="white")
        self.speed_label.configure(text="")
    
    def _format_time(self, seconds: float) -> str:
        """格式化时间显示"""
        if seconds < 60:
            return f"{int(seconds)}秒"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = int(seconds % 60)
            return f"{minutes}分{secs}秒"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}时{minutes}分"


class ConfigPanel(ctk.CTkFrame):
    """配置面板组件"""
    
    def __init__(self, parent, on_cookie_changed: Optional[Callable[[str], None]] = None):
        super().__init__(parent)
        
        self.on_cookie_changed = on_cookie_changed
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        # 标题
        title_label = ctk.CTkLabel(
            self, 
            text="⚙️ 配置设置", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=(10, 5))
        
        # Cookie设置
        cookie_frame = ctk.CTkFrame(self, fg_color="transparent")
        cookie_frame.pack(pady=10, padx=20, fill="x")
        
        cookie_label = ctk.CTkLabel(
            cookie_frame,
            text="抖音Cookie:",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        cookie_label.pack(anchor="w", pady=(0, 5))
        
        self.cookie_entry = ctk.CTkTextbox(
            cookie_frame,
            height=80
        )
        # 设置初始提示文本
        self.cookie_entry.insert("1.0", "请输入抖音创作者平台的Cookie...")
        self.cookie_entry.configure(text_color="gray")
        self.cookie_entry.pack(fill="x", pady=(0, 10))
        
        # 按钮框架
        button_frame = ctk.CTkFrame(cookie_frame, fg_color="transparent")
        button_frame.pack(fill="x")
        
        # 保存按钮
        save_button = ctk.CTkButton(
            button_frame,
            text="保存Cookie",
            command=self._save_cookie,
            width=100,
            height=30
        )
        save_button.pack(side="left")
        
        # 测试按钮
        test_button = ctk.CTkButton(
            button_frame,
            text="测试连接",
            command=self._test_cookie,
            width=100,
            height=30,
            fg_color="orange",
            hover_color="darkorange"
        )
        test_button.pack(side="left", padx=(10, 0))
        
        # 状态标签
        self.status_label = ctk.CTkLabel(
            cookie_frame,
            text="",
            font=ctk.CTkFont(size=11)
        )
        self.status_label.pack(anchor="w", pady=(10, 0))
    
    def _save_cookie(self):
        """保存Cookie"""
        cookie = self.cookie_entry.get("1.0", "end-1c").strip()

        # 检查是否为默认提示文本
        if not cookie or cookie == "请输入抖音创作者平台的Cookie...":
            messagebox.showwarning("警告", "请输入Cookie")
            return

        # 简单验证Cookie格式
        if "sessionid" not in cookie.lower():
            messagebox.showwarning("警告", "Cookie格式可能不正确，请确保包含sessionid")
            return
        
        # 保存到配置
        from ..config.settings import settings
        settings.set('douyin.cookie', cookie)
        
        self.status_label.configure(text="✅ Cookie已保存", text_color="green")
        
        # 调用回调函数
        if self.on_cookie_changed:
            self.on_cookie_changed(cookie)
    
    def _test_cookie(self):
        """测试Cookie"""
        cookie = self.cookie_entry.get("1.0", "end-1c").strip()

        # 检查是否为默认提示文本
        if not cookie or cookie == "请输入抖音创作者平台的Cookie...":
            messagebox.showwarning("警告", "请先输入Cookie")
            return
        
        self.status_label.configure(text="🔄 正在测试连接...", text_color="yellow")
        
        # 在新线程中测试连接
        import threading
        
        def test_connection():
            try:
                from ..core.auth import DouyinAuth
                auth = DouyinAuth(cookie)
                auth.get_upload_auth()
                
                # 更新UI（需要在主线程中执行）
                self.after(0, lambda: self.status_label.configure(
                    text="✅ 连接测试成功", text_color="green"
                ))
                
            except Exception as e:
                # 更新UI（需要在主线程中执行）
                self.after(0, lambda: self.status_label.configure(
                    text=f"❌ 连接测试失败: {str(e)[:50]}...", text_color="red"
                ))
        
        threading.Thread(target=test_connection, daemon=True).start()
    
    def get_cookie(self) -> str:
        """获取Cookie"""
        cookie = self.cookie_entry.get("1.0", "end-1c").strip()
        # 如果是默认提示文本，返回空字符串
        if cookie == "请输入抖音创作者平台的Cookie...":
            return ""
        return cookie
    
    def set_cookie(self, cookie: str):
        """设置Cookie"""
        self.cookie_entry.delete("1.0", "end")
        if cookie:
            self.cookie_entry.insert("1.0", cookie)
            self.cookie_entry.configure(text_color="white")
        else:
            self.cookie_entry.insert("1.0", "请输入抖音创作者平台的Cookie...")
            self.cookie_entry.configure(text_color="gray")
