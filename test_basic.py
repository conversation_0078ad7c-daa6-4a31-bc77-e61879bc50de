#!/usr/bin/env python3
"""
基础功能测试脚本
Basic functionality test script

测试各个模块的基本功能
Tests basic functionality of each module
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        from douyin_uploader.config.settings import settings
        print("✅ 配置模块导入成功")
        
        from douyin_uploader.core.utils import FileUtils, LogUtils
        print("✅ 工具模块导入成功")
        
        from douyin_uploader.core.auth import DouyinAuth
        print("✅ 认证模块导入成功")
        
        from douyin_uploader.core.uploader import DouyinUploader
        print("✅ 上传模块导入成功")
        
        from douyin_uploader.gui.components import FileSelector, ProgressBar, ConfigPanel
        print("✅ GUI组件模块导入成功")
        
        from douyin_uploader.gui.main_window import MainWindow
        print("✅ 主窗口模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_config():
    """测试配置功能"""
    print("\n🧪 测试配置功能...")
    
    try:
        from douyin_uploader.config.settings import settings
        
        # 测试配置读取
        auth_url = settings.get('douyin.auth_url')
        chunk_size = settings.get('upload.chunk_size_mb')
        
        print(f"✅ 认证URL: {auth_url}")
        print(f"✅ 切片大小: {chunk_size}MB")
        
        # 测试配置设置
        settings.set('test.value', 'hello')
        test_value = settings.get('test.value')
        
        if test_value == 'hello':
            print("✅ 配置读写功能正常")
            return True
        else:
            print("❌ 配置读写功能异常")
            return False
            
    except Exception as e:
        print(f"❌ 配置功能测试失败: {e}")
        return False

def test_file_utils():
    """测试文件工具"""
    print("\n🧪 测试文件工具...")
    
    try:
        from douyin_uploader.core.utils import FileUtils
        
        # 测试文件大小格式化
        size_str = FileUtils.format_file_size(1024 * 1024 * 5)  # 5MB
        print(f"✅ 文件大小格式化: {size_str}")
        
        # 测试CRC32计算
        test_data = b"Hello, World!"
        crc32 = FileUtils.calculate_crc32(test_data)
        print(f"✅ CRC32计算: {crc32}")
        
        # 测试文件类型检测
        image_type = FileUtils.get_file_type("test.jpg")
        video_type = FileUtils.get_file_type("test.mp4")
        unknown_type = FileUtils.get_file_type("test.txt")
        
        print(f"✅ 文件类型检测: jpg={image_type}, mp4={video_type}, txt={unknown_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件工具测试失败: {e}")
        return False

def test_log_utils():
    """测试日志工具"""
    print("\n🧪 测试日志工具...")
    
    try:
        from douyin_uploader.core.utils import LogUtils
        
        # 测试日志记录
        LogUtils.info("这是一条测试信息")
        LogUtils.warning("这是一条测试警告")
        LogUtils.error("这是一条测试错误")
        
        print("✅ 日志功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 日志工具测试失败: {e}")
        return False

def test_gui_imports():
    """测试GUI模块导入"""
    print("\n🧪 测试GUI模块...")
    
    try:
        import customtkinter as ctk
        print("✅ CustomTkinter导入成功")
        
        # 测试创建基本组件（不显示）
        root = ctk.CTk()
        root.withdraw()  # 隐藏窗口
        
        label = ctk.CTkLabel(root, text="测试")
        button = ctk.CTkButton(root, text="测试")
        
        print("✅ GUI组件创建成功")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI模块测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始基础功能测试\n")
    
    tests = [
        ("模块导入", test_imports),
        ("配置功能", test_config),
        ("文件工具", test_file_utils),
        ("日志工具", test_log_utils),
        ("GUI模块", test_gui_imports),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础功能测试通过！")
        print("\n💡 使用方法:")
        print("   python main.py")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关模块")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
