"""
主窗口模块
Main Window Module

实现应用程序的主界面
Implements the main interface of the application
"""

import tkinter as tk
from tkinter import messagebox
import customtkinter as ctk
import threading
import os

from .components import FileSelector, ProgressBar, ConfigPanel
from ..core.auth import <PERSON><PERSON><PERSON>Auth
from ..core.uploader import DouyinUploader
from ..core.utils import LogUtils
from ..config.settings import settings


class MainWindow:
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        # 设置CustomTkinter外观
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # 创建主窗口
        self.root = ctk.CTk()
        self.root.title("抖音创作者平台上传工具 v1.0.0")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # 设置窗口图标（如果有的话）
        try:
            # 这里可以设置窗口图标
            pass
        except:
            pass
        
        # 初始化变量
        self.auth = None
        self.uploader = None
        self.is_uploading = False
        
        # 设置UI
        self._setup_ui()
        
        # 加载保存的配置
        self._load_config()
        
        LogUtils.info("主窗口初始化完成")
    
    def _setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_label = ctk.CTkLabel(
            self.root,
            text="🚀 抖音创作者平台上传工具",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 10))
        
        # 副标题
        subtitle_label = ctk.CTkLabel(
            self.root,
            text="支持图片和视频上传，大文件自动切片上传",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        subtitle_label.pack(pady=(0, 20))
        
        # 创建主要内容区域
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        # 左侧面板
        left_panel = ctk.CTkFrame(main_frame)
        left_panel.pack(side="left", fill="both", expand=True, padx=(10, 5), pady=10)
        
        # 右侧面板
        right_panel = ctk.CTkFrame(main_frame)
        right_panel.pack(side="right", fill="y", padx=(5, 10), pady=10)
        right_panel.configure(width=300)
        
        # 文件选择器
        self.file_selector = FileSelector(
            left_panel, 
            on_file_selected=self._on_file_selected
        )
        self.file_selector.pack(fill="x", padx=10, pady=(10, 5))
        
        # 进度条
        self.progress_bar = ProgressBar(left_panel)
        self.progress_bar.pack(fill="x", padx=10, pady=5)
        
        # 上传按钮区域
        button_frame = ctk.CTkFrame(left_panel, fg_color="transparent")
        button_frame.pack(fill="x", padx=10, pady=10)
        
        # 上传按钮
        self.upload_button = ctk.CTkButton(
            button_frame,
            text="🚀 开始上传",
            command=self._start_upload,
            width=150,
            height=40,
            font=ctk.CTkFont(size=16, weight="bold"),
            state="disabled"
        )
        self.upload_button.pack(side="left", padx=(0, 10))
        
        # 取消按钮
        self.cancel_button = ctk.CTkButton(
            button_frame,
            text="❌ 取消上传",
            command=self._cancel_upload,
            width=120,
            height=40,
            fg_color="red",
            hover_color="darkred",
            state="disabled"
        )
        self.cancel_button.pack(side="left")
        
        # 结果显示区域
        result_frame = ctk.CTkFrame(left_panel)
        result_frame.pack(fill="both", expand=True, padx=10, pady=(5, 10))
        
        result_title = ctk.CTkLabel(
            result_frame,
            text="📋 上传结果",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        result_title.pack(pady=(10, 5))
        
        self.result_text = ctk.CTkTextbox(
            result_frame,
            height=150,
            font=ctk.CTkFont(size=11)
        )
        self.result_text.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # 配置面板
        self.config_panel = ConfigPanel(
            right_panel,
            on_cookie_changed=self._on_cookie_changed
        )
        self.config_panel.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 状态栏
        self.status_bar = ctk.CTkLabel(
            self.root,
            text="就绪",
            font=ctk.CTkFont(size=11),
            text_color="gray",
            anchor="w"
        )
        self.status_bar.pack(side="bottom", fill="x", padx=20, pady=(0, 10))
    
    def _load_config(self):
        """加载保存的配置"""
        try:
            # 加载Cookie
            saved_cookie = settings.get('douyin.cookie', '')
            if saved_cookie:
                self.config_panel.set_cookie(saved_cookie)
                self._on_cookie_changed(saved_cookie)
                
        except Exception as e:
            LogUtils.error(f"加载配置失败: {e}")
    
    def _on_file_selected(self, file_path: str):
        """文件选择回调"""
        if file_path and self.auth:
            self.upload_button.configure(state="normal")
            self.status_bar.configure(text=f"已选择文件: {os.path.basename(file_path)}")
        else:
            self.upload_button.configure(state="disabled")
            self.status_bar.configure(text="就绪")
    
    def _on_cookie_changed(self, cookie: str):
        """Cookie变更回调"""
        try:
            if cookie:
                self.auth = DouyinAuth(cookie)
                self.uploader = DouyinUploader(self.auth)
                
                # 如果已选择文件，启用上传按钮
                if self.file_selector.get_selected_file():
                    self.upload_button.configure(state="normal")
                
                self.status_bar.configure(text="认证已设置")
                LogUtils.info("认证设置成功")
            else:
                self.auth = None
                self.uploader = None
                self.upload_button.configure(state="disabled")
                self.status_bar.configure(text="请设置Cookie")
                
        except Exception as e:
            LogUtils.error(f"设置认证失败: {e}")
            messagebox.showerror("错误", f"设置认证失败: {e}")
    
    def _start_upload(self):
        """开始上传"""
        if self.is_uploading:
            return
        
        file_path = self.file_selector.get_selected_file()
        if not file_path:
            messagebox.showwarning("警告", "请先选择文件")
            return
        
        if not self.uploader:
            messagebox.showwarning("警告", "请先设置Cookie")
            return
        
        # 更新UI状态
        self.is_uploading = True
        self.upload_button.configure(state="disabled")
        self.cancel_button.configure(state="normal")
        self.progress_bar.reset()
        self.progress_bar.set_status("准备上传...", "yellow")
        self.result_text.delete("1.0", "end")
        
        # 在新线程中执行上传
        upload_thread = threading.Thread(
            target=self._upload_worker,
            args=(file_path,),
            daemon=True
        )
        upload_thread.start()
    
    def _upload_worker(self, file_path: str):
        """上传工作线程"""
        try:
            LogUtils.info(f"开始上传文件: {file_path}")
            
            # 更新状态
            self.root.after(0, lambda: self.progress_bar.set_status("正在上传...", "blue"))
            
            # 执行上传
            result = self.uploader.upload_file(
                file_path,
                progress_callback=self._on_upload_progress
            )
            
            # 上传成功
            self.root.after(0, lambda: self._on_upload_success(result))
            
        except Exception as e:
            # 上传失败
            error_msg = str(e)
            LogUtils.error(f"上传失败: {error_msg}")
            self.root.after(0, lambda: self._on_upload_error(error_msg))
    
    def _on_upload_progress(self, uploaded: int, total: int, speed: float):
        """上传进度回调"""
        # 在主线程中更新UI
        self.root.after(0, lambda: self.progress_bar.update_progress(uploaded, total, speed))
    
    def _on_upload_success(self, result: dict):
        """上传成功处理"""
        self.is_uploading = False
        self.upload_button.configure(state="normal")
        self.cancel_button.configure(state="disabled")
        
        self.progress_bar.set_status("✅ 上传完成", "green")
        
        # 显示结果
        result_text = f"上传成功！\n\n"
        result_text += f"文件类型: {result.get('file_type', 'unknown')}\n"
        result_text += f"存储URI: {result.get('store_uri', 'unknown')}\n"
        result_text += f"上传地址: {result.get('upload_url', 'unknown')}\n"
        
        if 'chunks' in result:
            result_text += f"切片数量: {result['chunks']}\n"
        
        import datetime
        result_text += f"\n时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        self.result_text.insert("1.0", result_text)
        self.status_bar.configure(text="上传完成")
        
        LogUtils.info("文件上传成功")
        messagebox.showinfo("成功", "文件上传成功！")
    
    def _on_upload_error(self, error_msg: str):
        """上传失败处理"""
        self.is_uploading = False
        self.upload_button.configure(state="normal")
        self.cancel_button.configure(state="disabled")
        
        self.progress_bar.set_status("❌ 上传失败", "red")
        
        # 显示错误信息
        error_text = f"上传失败！\n\n错误信息: {error_msg}\n\n"
        error_text += "请检查:\n"
        error_text += "1. 网络连接是否正常\n"
        error_text += "2. Cookie是否有效\n"
        error_text += "3. 文件是否损坏\n"
        
        self.result_text.insert("1.0", error_text)
        self.status_bar.configure(text="上传失败")
        
        messagebox.showerror("错误", f"上传失败: {error_msg}")
    
    def _cancel_upload(self):
        """取消上传"""
        if self.is_uploading:
            # 这里可以实现取消上传的逻辑
            # 由于当前实现比较简单，暂时只更新UI状态
            self.is_uploading = False
            self.upload_button.configure(state="normal")
            self.cancel_button.configure(state="disabled")
            self.progress_bar.set_status("❌ 已取消", "orange")
            self.status_bar.configure(text="上传已取消")
            
            LogUtils.info("用户取消上传")
    
    def run(self):
        """运行应用程序"""
        try:
            LogUtils.info("启动GUI应用程序")
            self.root.mainloop()
        except KeyboardInterrupt:
            LogUtils.info("用户中断程序")
        except Exception as e:
            LogUtils.error(f"程序运行异常: {e}")
            raise
        finally:
            LogUtils.info("程序退出")
